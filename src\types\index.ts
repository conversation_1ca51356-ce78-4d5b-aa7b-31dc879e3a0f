// Core APIX Protocol Types
export interface APIDXRequest {
  userId: string;
  sessionId: string;
  message: string;
  appType: 'widget' | 'dashboard' | 'crm';
  metadata?: Record<string, unknown>;
}

export interface APIDXResponse {
  stream?: boolean;
  chunks?: string[];
  final?: string;
  tool_call?: {
    toolId: string;
    params: Record<string, unknown>;
  };
  error?: string;
  state_update?: Record<string, unknown>;
}

// Authentication & RBAC Types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: UserRole;
  organizations: UserOrganization[];
  createdAt: Date;
  lastActiveAt: Date;
}

export type UserRole = 'super_admin' | 'org_admin' | 'developer' | 'business_user' | 'viewer' | 'auditor';

export interface UserOrganization {
  id: string;
  name: string;
  role: UserRole;
  permissions: Permission[];
}

export interface Permission {
  resource: 'agents' | 'tools' | 'workflows' | 'knowledge' | 'prompts';
  actions: ('create' | 'read' | 'update' | 'delete' | 'execute' | 'share')[];
}

// AI Provider Types
export interface AIProvider {
  id: string;
  name: string;
  type: 'openai' | 'anthropic' | 'google' | 'groq' | 'mistral' | 'custom';
  models: AIModel[];
  apiKey: string;
  baseUrl?: string;
  isActive: boolean;
  healthStatus: 'healthy' | 'degraded' | 'unhealthy';
  usage: ProviderUsage;
}

export interface AIModel {
  id: string;
  name: string;
  description: string;
  inputTokenCost: number;
  outputTokenCost: number;
  contextLength: number;
  capabilities: ModelCapability[];
}

export type ModelCapability = 'text' | 'vision' | 'audio' | 'function_calling' | 'json_mode';

export interface ProviderUsage {
  totalTokens: number;
  totalCost: number;
  requestCount: number;
  avgLatency: number;
  errorRate: number;
}

// Agent Types
export interface Agent {
  id: string;
  name: string;
  description: string;
  type: 'single_task' | 'multi_task' | 'collaborative' | 'supervisory';
  providerId: string;
  modelId: string;
  systemPrompt: string;
  memory: AgentMemory;
  tools: string[];
  isActive: boolean;
  performance: AgentPerformance;
  createdAt: Date;
  updatedAt: Date;
}

export interface AgentMemory {
  shortTerm: Record<string, unknown>;
  longTerm: Record<string, unknown>;
  episodic: AgentEpisode[];
  semantic: Record<string, unknown>;
}

export interface AgentEpisode {
  id: string;
  timestamp: Date;
  context: string;
  action: string;
  result: string;
  feedback?: number;
}

export interface AgentPerformance {
  totalInteractions: number;
  avgResponseTime: number;
  successRate: number;
  userSatisfaction: number;
  costPerInteraction: number;
}

// Tool Types
export interface Tool {
  id: string;
  name: string;
  description: string;
  category: ToolCategory;
  version: string;
  schema: ToolSchema;
  authentication: ToolAuth;
  isInstalled: boolean;
  healthStatus: 'healthy' | 'degraded' | 'unhealthy';
  usage: ToolUsage;
}

export type ToolCategory = 'communication' | 'crm' | 'finance' | 'development' | 'marketing' | 'productivity' | 'custom';

export interface ToolSchema {
  input: Record<string, unknown>;
  output: Record<string, unknown>;
  required: string[];
}

export interface ToolAuth {
  type: 'none' | 'api_key' | 'oauth2' | 'basic';
  config: Record<string, unknown>;
}

export interface ToolUsage {
  executionCount: number;
  successRate: number;
  avgExecutionTime: number;
  errorCount: number;
}

// Workflow Types
export interface Workflow {
  id: string;
  name: string;
  description: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  triggers: WorkflowTrigger[];
  isActive: boolean;
  version: number;
  performance: WorkflowPerformance;
}

export interface WorkflowNode {
  id: string;
  type: 'agent' | 'tool' | 'condition' | 'loop' | 'human_approval' | 'parallel' | 'delay' | 'webhook';
  position: { x: number; y: number };
  data: Record<string, unknown>;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  condition?: string;
}

export interface WorkflowTrigger {
  type: 'manual' | 'schedule' | 'webhook' | 'event';
  config: Record<string, unknown>;
}

export interface WorkflowPerformance {
  totalExecutions: number;
  successRate: number;
  avgExecutionTime: number;
  costPerExecution: number;
}

// Knowledge Base Types
export interface Document {
  id: string;
  name: string;
  type: 'pdf' | 'docx' | 'txt' | 'csv' | 'html' | 'markdown' | 'image';
  content: string;
  metadata: DocumentMetadata;
  embeddings: number[];
  isProcessed: boolean;
  uploadedAt: Date;
}

export interface DocumentMetadata {
  size: number;
  language: string;
  author?: string;
  tags: string[];
  summary: string;
}

export interface KnowledgeQuery {
  query: string;
  results: KnowledgeResult[];
  timestamp: Date;
}

export interface KnowledgeResult {
  document: Document;
  score: number;
  relevantChunks: string[];
}

// Prompt Types
export interface Prompt {
  id: string;
  name: string;
  description: string;
  template: string;
  variables: PromptVariable[];
  versions: PromptVersion[];
  performance: PromptPerformance;
}

export interface PromptVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  defaultValue?: unknown;
}

export interface PromptVersion {
  id: string;
  version: string;
  template: string;
  changelog: string;
  createdAt: Date;
  isActive: boolean;
}

export interface PromptPerformance {
  totalUsage: number;
  avgQualityScore: number;
  avgResponseTime: number;
  successRate: number;
}

// WebSocket Event Types
export type WebSocketEvent = 
  | 'user_message'
  | 'thinking_status'
  | 'text_chunk'
  | 'tool_call_start'
  | 'tool_call_result'
  | 'tool_call_error'
  | 'request_user_input'
  | 'user_response'
  | 'state_update'
  | 'workflow_step'
  | 'agent_handoff'
  | 'hybrid_execution'
  | 'knowledge_retrieval'
  | 'prompt_optimization';

export interface WebSocketMessage {
  event: WebSocketEvent;
  data: unknown;
  timestamp: Date;
  sessionId: string;
  userId: string;
}