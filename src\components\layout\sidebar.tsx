import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { useAppStore } from '@/lib/stores/app-store';
import { useAuthStore } from '@/lib/stores/auth-store';
import type { AppState } from '@/lib/stores/app-store';
import {
  Brain,
  Wrench,
  GitBranch,
  Database,
  FileText,
  Zap,
  BarChart3,
  Settings,
  ChevronLeft,
  ChevronRight,
  Activity,
  Users,
  Shield,
  Sparkles,
} from 'lucide-react';

const navigationItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: BarChart3,
    description: 'System overview and analytics',
  },
  {
    id: 'agents',
    label: 'AI Agents',
    icon: Brain,
    description: 'Intelligent agents and automation',
  },
  {
    id: 'tools',
    label: 'Tools',
    icon: Wrench,
    description: 'Business tools and integrations',
  },
  {
    id: 'workflows',
    label: 'Workflows',
    icon: GitBranch,
    description: 'Visual workflow automation',
  },
  {
    id: 'knowledge',
    label: 'Knowledge Base',
    icon: Database,
    description: 'RAG and document management',
  },
  {
    id: 'prompts',
    label: 'Prompts',
    icon: FileText,
    description: 'Prompt templates and optimization',
  },
  {
    id: 'providers',
    label: 'AI Providers',
    icon: Zap,
    description: 'AI provider management and routing',
  },
];

const adminItems = [
  {
    id: 'analytics',
    label: 'Analytics',
    icon: Activity,
    description: 'System performance analytics',
  },
  {
    id: 'users',
    label: 'User Management',
    icon: Users,
    description: 'User and organization management',
  },
  {
    id: 'security',
    label: 'Security',
    icon: Shield,
    description: 'Security and compliance settings',
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    description: 'System configuration',
  },
];

export function Sidebar() {
  const { 
    currentView, 
    setCurrentView, 
    sidebarCollapsed, 
    toggleSidebar,
    activeConnections,
    systemHealth 
  } = useAppStore();
  const { user } = useAuthStore();
  
  const isAdmin = user?.role === 'super_admin' || user?.role === 'org_admin';

  const handleNavigation = (viewId: string) => {
    setCurrentView(viewId as AppState['currentView']);
  };

  return (
    <div className={cn(
      "flex flex-col h-full bg-gradient-to-b from-gray-50 to-white border-r border-gray-200 transition-all duration-300",
      sidebarCollapsed ? "w-16" : "w-64"
    )}>
      {/* Header */}
      <div className="flex items-center gap-3 p-4 border-b border-gray-200">
        <div className="flex items-center gap-2 min-w-0">
          <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          {!sidebarCollapsed && (
            <div className="min-w-0">
              <h1 className="font-bold text-gray-900 truncate">SynapseAI</h1>
              <p className="text-xs text-gray-500">AI Orchestration</p>
            </div>
          )}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleSidebar}
          className="flex-shrink-0 w-8 h-8 p-0"
        >
          {sidebarCollapsed ? (
            <ChevronRight className="w-4 h-4" />
          ) : (
            <ChevronLeft className="w-4 h-4" />
          )}
        </Button>
      </div>

      {/* System Status */}
      {!sidebarCollapsed && (
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">System Status</span>
            <Badge variant={systemHealth === 'healthy' ? 'default' : systemHealth === 'degraded' ? 'secondary' : 'destructive'}>
              {systemHealth}
            </Badge>
          </div>
          <div className="text-xs text-gray-500">
            Active connections: {activeConnections}
          </div>
        </div>
      )}

      <ScrollArea className="flex-1">
        <div className="p-2">
          {/* Main Navigation */}
          <div className="space-y-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentView === item.id;
              
              return (
                <Button
                  key={item.id}
                  variant={isActive ? "default" : "ghost"}
                  onClick={() => handleNavigation(item.id)}
                  className={cn(
                    "w-full justify-start h-10 px-3",
                    sidebarCollapsed && "px-2",
                    isActive && "bg-blue-50 text-blue-700 hover:bg-blue-100"
                  )}
                >
                  <Icon className={cn("flex-shrink-0", sidebarCollapsed ? "w-4 h-4" : "w-4 h-4 mr-3")} />
                  {!sidebarCollapsed && (
                    <div className="flex-1 text-left">
                      <div className="font-medium">{item.label}</div>
                      <div className="text-xs text-muted-foreground">{item.description}</div>
                    </div>
                  )}
                </Button>
              );
            })}
          </div>

          {/* Admin Section */}
          {isAdmin && !sidebarCollapsed && (
            <>
              <Separator className="my-4" />
              <div className="px-3 mb-2">
                <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide">
                  Administration
                </h3>
              </div>
              <div className="space-y-1">
                {adminItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = currentView === item.id;
                  
                  return (
                    <Button
                      key={item.id}
                      variant={isActive ? "default" : "ghost"}
                      onClick={() => handleNavigation(item.id)}
                      className={cn(
                        "w-full justify-start h-10 px-3",
                        isActive && "bg-blue-50 text-blue-700 hover:bg-blue-100"
                      )}
                    >
                      <Icon className="w-4 h-4 mr-3 flex-shrink-0" />
                      <div className="flex-1 text-left">
                        <div className="font-medium">{item.label}</div>
                        <div className="text-xs text-muted-foreground">{item.description}</div>
                      </div>
                    </Button>
                  );
                })}
              </div>
            </>
          )}
        </div>
      </ScrollArea>

      {/* User Info */}
      {!sidebarCollapsed && user && (
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
              {user.name.charAt(0).toUpperCase()}
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm text-gray-900 truncate">
                {user.name}
              </div>
              <div className="text-xs text-gray-500 truncate">
                {user.role.replace('_', ' ')}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}