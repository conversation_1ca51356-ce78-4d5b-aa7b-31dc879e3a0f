import { useState } from 'react';
import { useAppStore } from '@/lib/stores/app-store';
import { useAuthStore } from '@/lib/stores/auth-store';
import type { AppState } from '@/lib/stores/app-store';
import {
  Brain,
  Wrench,
  GitBranch,
  Database,
  FileText,
  Zap,
  BarChart3,
  Settings,
  Activity,
  Users,
  Shield,
  Sparkles,
} from 'lucide-react';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import { Badge } from '@/components/ui/badge';

const navigationItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: BarChart3,
    description: 'System overview and analytics',
  },
  {
    id: 'agents',
    label: 'AI Agents',
    icon: Brain,
    description: 'Intelligent agents and automation',
  },
  {
    id: 'tools',
    label: 'Tools',
    icon: Wrench,
    description: 'Business tools and integrations',
  },
  {
    id: 'workflows',
    label: 'Workflows',
    icon: GitBranch,
    description: 'Visual workflow automation',
  },
  {
    id: 'knowledge',
    label: 'Knowledge Base',
    icon: Database,
    description: 'RAG and document management',
  },
  {
    id: 'prompts',
    label: 'Prompts',
    icon: FileText,
    description: 'Prompt templates and optimization',
  },
  {
    id: 'providers',
    label: 'AI Providers',
    icon: Zap,
    description: 'AI provider management and routing',
  },
];

const adminItems = [
  {
    id: 'analytics',
    label: 'Analytics',
    icon: Activity,
    description: 'System performance analytics',
  },
  {
    id: 'users',
    label: 'User Management',
    icon: Users,
    description: 'User and organization management',
  },
  {
    id: 'security',
    label: 'Security',
    icon: Shield,
    description: 'Security and compliance settings',
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    description: 'System configuration',
  },
];

export function AppSidebar() {
  const { 
    currentView, 
    setCurrentView, 
    activeConnections,
    systemHealth 
  } = useAppStore();
  const { user } = useAuthStore();
  
  const isAdmin = user?.role === 'super_admin' || user?.role === 'org_admin';

  const handleNavigation = (viewId: string) => {
    setCurrentView(viewId as AppState['currentView']);
  };

  return (
    <Sidebar>
      <SidebarHeader className="border-b border-sidebar-border">
        <div className="flex items-center gap-2 px-2 py-2">
          <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div className="min-w-0">
            <h1 className="font-bold text-sidebar-foreground truncate">SynapseAI</h1>
            <p className="text-xs text-sidebar-foreground/70">AI Orchestration</p>
          </div>
        </div>
        
        {/* System Status */}
        <div className="px-2 py-2">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium text-sidebar-foreground/70">System Status</span>
            <Badge variant={systemHealth === 'healthy' ? 'default' : systemHealth === 'degraded' ? 'secondary' : 'destructive'}>
              {systemHealth}
            </Badge>
          </div>
          <div className="text-xs text-sidebar-foreground/50">
            Active connections: {activeConnections}
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentView === item.id;
                
                return (
                  <SidebarMenuItem key={item.id}>
                    <SidebarMenuButton
                      onClick={() => handleNavigation(item.id)}
                      isActive={isActive}
                      tooltip={item.description}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{item.label}</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Admin Section */}
        {isAdmin && (
          <>
            <SidebarSeparator />
            <SidebarGroup>
              <SidebarGroupLabel>Administration</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {adminItems.map((item) => {
                    const Icon = item.icon;
                    const isActive = currentView === item.id;
                    
                    return (
                      <SidebarMenuItem key={item.id}>
                        <SidebarMenuButton
                          onClick={() => handleNavigation(item.id)}
                          isActive={isActive}
                          tooltip={item.description}
                        >
                          <Icon className="w-4 h-4" />
                          <span>{item.label}</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    );
                  })}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </>
        )}
      </SidebarContent>

      <SidebarFooter className="border-t border-sidebar-border">
        {user && (
          <div className="flex items-center gap-3 px-2 py-2">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
              {user.name.charAt(0).toUpperCase()}
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm text-sidebar-foreground truncate">
                {user.name}
              </div>
              <div className="text-xs text-sidebar-foreground/70 truncate">
                {user.role.replace('_', ' ')}
              </div>
            </div>
          </div>
        )}
      </SidebarFooter>
    </Sidebar>
  );
}
