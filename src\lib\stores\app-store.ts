import { create } from 'zustand';
import { Agent, Tool, Workflow, AIProvider, Document, Prompt } from '@/types';

export interface AppState {
  // Navigation
  currentView: 'dashboard' | 'agents' | 'tools' | 'workflows' | 'knowledge' | 'prompts' | 'providers' | 'analytics';
  
  // Data
  agents: Agent[];
  tools: Tool[];
  workflows: Workflow[];
  providers: AIProvider[];
  documents: Document[];
  prompts: Prompt[];
  
  // UI State
  sidebarCollapsed: boolean;
  loading: Record<string, boolean>;
  errors: Record<string, string>;
  
  // Real-time data
  activeConnections: number;
  systemHealth: 'healthy' | 'degraded' | 'unhealthy';
  
  // Selected items
  selectedAgent: Agent | null;
  selectedTool: Tool | null;
  selectedWorkflow: Workflow | null;
}

interface AppActions {
  // Navigation
  setCurrentView: (view: AppState['currentView']) => void;
  toggleSidebar: () => void;
  
  // Data management
  setAgents: (agents: Agent[]) => void;
  addAgent: (agent: Agent) => void;
  updateAgent: (id: string, updates: Partial<Agent>) => void;
  removeAgent: (id: string) => void;
  
  setTools: (tools: Tool[]) => void;
  addTool: (tool: Tool) => void;
  updateTool: (id: string, updates: Partial<Tool>) => void;
  removeTool: (id: string) => void;
  
  setWorkflows: (workflows: Workflow[]) => void;
  addWorkflow: (workflow: Workflow) => void;
  updateWorkflow: (id: string, updates: Partial<Workflow>) => void;
  removeWorkflow: (id: string) => void;
  
  setProviders: (providers: AIProvider[]) => void;
  addProvider: (provider: AIProvider) => void;
  updateProvider: (id: string, updates: Partial<AIProvider>) => void;
  removeProvider: (id: string) => void;
  
  setDocuments: (documents: Document[]) => void;
  addDocument: (document: Document) => void;
  updateDocument: (id: string, updates: Partial<Document>) => void;
  removeDocument: (id: string) => void;
  
  setPrompts: (prompts: Prompt[]) => void;
  addPrompt: (prompt: Prompt) => void;
  updatePrompt: (id: string, updates: Partial<Prompt>) => void;
  removePrompt: (id: string) => void;
  
  // Selection
  selectAgent: (agent: Agent | null) => void;
  selectTool: (tool: Tool | null) => void;
  selectWorkflow: (workflow: Workflow | null) => void;
  
  // UI state
  setLoading: (key: string, loading: boolean) => void;
  setError: (key: string, error: string) => void;
  clearError: (key: string) => void;
  
  // System state
  setSystemHealth: (health: AppState['systemHealth']) => void;
  setActiveConnections: (count: number) => void;
}

type AppStore = AppState & AppActions;

export const useAppStore = create<AppStore>((set, get) => ({
  // Initial state
  currentView: 'dashboard',
  agents: [],
  tools: [],
  workflows: [],
  providers: [],
  documents: [],
  prompts: [],
  sidebarCollapsed: false,
  loading: {},
  errors: {},
  activeConnections: 0,
  systemHealth: 'healthy',
  selectedAgent: null,
  selectedTool: null,
  selectedWorkflow: null,

  // Navigation actions
  setCurrentView: (view) => set({ currentView: view }),
  toggleSidebar: () => set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed })),

  // Agent actions
  setAgents: (agents) => set({ agents }),
  addAgent: (agent) => set((state) => ({ agents: [...state.agents, agent] })),
  updateAgent: (id, updates) => set((state) => ({
    agents: state.agents.map((agent) => 
      agent.id === id ? { ...agent, ...updates } : agent
    )
  })),
  removeAgent: (id) => set((state) => ({
    agents: state.agents.filter((agent) => agent.id !== id)
  })),

  // Tool actions
  setTools: (tools) => set({ tools }),
  addTool: (tool) => set((state) => ({ tools: [...state.tools, tool] })),
  updateTool: (id, updates) => set((state) => ({
    tools: state.tools.map((tool) => 
      tool.id === id ? { ...tool, ...updates } : tool
    )
  })),
  removeTool: (id) => set((state) => ({
    tools: state.tools.filter((tool) => tool.id !== id)
  })),

  // Workflow actions
  setWorkflows: (workflows) => set({ workflows }),
  addWorkflow: (workflow) => set((state) => ({ workflows: [...state.workflows, workflow] })),
  updateWorkflow: (id, updates) => set((state) => ({
    workflows: state.workflows.map((workflow) => 
      workflow.id === id ? { ...workflow, ...updates } : workflow
    )
  })),
  removeWorkflow: (id) => set((state) => ({
    workflows: state.workflows.filter((workflow) => workflow.id !== id)
  })),

  // Provider actions
  setProviders: (providers) => set({ providers }),
  addProvider: (provider) => set((state) => ({ providers: [...state.providers, provider] })),
  updateProvider: (id, updates) => set((state) => ({
    providers: state.providers.map((provider) => 
      provider.id === id ? { ...provider, ...updates } : provider
    )
  })),
  removeProvider: (id) => set((state) => ({
    providers: state.providers.filter((provider) => provider.id !== id)
  })),

  // Document actions
  setDocuments: (documents) => set({ documents }),
  addDocument: (document) => set((state) => ({ documents: [...state.documents, document] })),
  updateDocument: (id, updates) => set((state) => ({
    documents: state.documents.map((document) => 
      document.id === id ? { ...document, ...updates } : document
    )
  })),
  removeDocument: (id) => set((state) => ({
    documents: state.documents.filter((document) => document.id !== id)
  })),

  // Prompt actions
  setPrompts: (prompts) => set({ prompts }),
  addPrompt: (prompt) => set((state) => ({ prompts: [...state.prompts, prompt] })),
  updatePrompt: (id, updates) => set((state) => ({
    prompts: state.prompts.map((prompt) => 
      prompt.id === id ? { ...prompt, ...updates } : prompt
    )
  })),
  removePrompt: (id) => set((state) => ({
    prompts: state.prompts.filter((prompt) => prompt.id !== id)
  })),

  // Selection actions
  selectAgent: (agent) => set({ selectedAgent: agent }),
  selectTool: (tool) => set({ selectedTool: tool }),
  selectWorkflow: (workflow) => set({ selectedWorkflow: workflow }),

  // UI state actions
  setLoading: (key, loading) => set((state) => ({
    loading: { ...state.loading, [key]: loading }
  })),
  setError: (key, error) => set((state) => ({
    errors: { ...state.errors, [key]: error }
  })),
  clearError: (key) => set((state) => {
    const { [key]: removed, ...rest } = state.errors;
    return { errors: rest };
  }),

  // System state actions
  setSystemHealth: (health) => set({ systemHealth: health }),
  setActiveConnections: (count) => set({ activeConnections: count }),
}));