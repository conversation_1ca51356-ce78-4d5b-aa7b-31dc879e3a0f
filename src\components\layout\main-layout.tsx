import { AppSidebar } from './app-sidebar';
import { Header } from './header';
import { useAppStore } from '@/lib/stores/app-store';
import { useAuthStore } from '@/lib/stores/auth-store';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';

// Import page components
import Dashboard from '@/pages/Dashboard';
import AgentsPage from '@/pages/Agents';
import ToolsPage from '@/pages/Tools';
import WorkflowsPage from '@/pages/Workflows';
import KnowledgePage from '@/pages/Knowledge';
import PromptsPage from '@/pages/Prompts';
import ProvidersPage from '@/pages/Providers';
import AnalyticsPage from '@/pages/Analytics';

const pageComponents = {
  dashboard: Dashboard,
  agents: AgentsPage,
  tools: ToolsPage,
  workflows: WorkflowsPage,
  knowledge: KnowledgePage,
  prompts: PromptsPage,
  providers: ProvidersPage,
  analytics: AnalyticsPage,
};

export function MainLayout() {
  const { currentView } = useAppStore();
  const { isAuthenticated } = useAuthStore();

  const CurrentPageComponent = pageComponents[currentView] || Dashboard;

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Please log in</h1>
          <p className="text-gray-600">You need to be authenticated to access SynapseAI</p>
        </div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <Header />
        <main className="flex-1 overflow-auto">
          <div className="p-8">
            <CurrentPageComponent />
          </div>
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}