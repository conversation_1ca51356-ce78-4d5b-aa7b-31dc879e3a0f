import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAppStore } from '@/lib/stores/app-store';
import { 
  Zap, 
  Plus, 
  Settings, 
  Activity, 
  Clock, 
  DollarSign, 
  CheckCircle,
  AlertTriangle,
  XCircle
} from 'lucide-react';

export default function ProvidersPage() {
  const { providers } = useAppStore();

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'degraded':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'unhealthy':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <Zap className="h-8 w-8 text-yellow-600" />
            AI Providers
          </h1>
          <p className="mt-2 text-gray-600">
            Manage AI providers and configure intelligent routing for optimal performance.
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Provider
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Providers</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{providers.filter(p => p.isActive).length}</div>
            <p className="text-xs text-muted-foreground">{providers.length} total configured</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {providers.reduce((sum, provider) => sum + provider.usage.requestCount, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">+28% this month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Latency</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(providers.reduce((sum, provider) => sum + provider.usage.avgLatency, 0) / providers.length)}ms
            </div>
            <p className="text-xs text-muted-foreground">-15ms improvement</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${providers.reduce((sum, provider) => sum + provider.usage.totalCost, 0).toFixed(0)}
            </div>
            <p className="text-xs text-muted-foreground">-8% cost reduction</p>
          </CardContent>
        </Card>
      </div>

      {/* Providers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {providers.map((provider) => (
          <Card key={provider.id} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-50 rounded-lg">
                  <Zap className="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <CardTitle className="text-lg">{provider.name}</CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge variant={provider.isActive ? "default" : "secondary"} className="text-xs">
                      {provider.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                    {getHealthIcon(provider.healthStatus)}
                  </div>
                </div>
              </div>
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Models</span>
                  <span className="font-medium">{provider.models.length}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Requests</span>
                  <span className="font-medium">{provider.usage.requestCount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Error Rate</span>
                  <span className="font-medium">{(provider.usage.errorRate * 100).toFixed(2)}%</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-sm font-bold text-green-600">
                    {provider.usage.avgLatency}ms
                  </div>
                  <div className="text-xs text-gray-500">Avg Latency</div>
                </div>
                <div className="text-center">
                  <div className="text-sm font-bold text-blue-600">
                    ${provider.usage.totalCost.toFixed(0)}
                  </div>
                  <div className="text-xs text-gray-500">Total Cost</div>
                </div>
              </div>

              <Button variant="outline" size="sm" className="w-full">
                Configure Models
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {providers.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <CardTitle className="text-xl text-gray-600 mb-2">No providers configured</CardTitle>
            <CardDescription className="mb-4">
              Get started by adding your first AI provider to enable intelligent routing.
            </CardDescription>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Provider
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}