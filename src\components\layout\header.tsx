import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useAppStore } from '@/lib/stores/app-store';
import {
  Search,
  Bell,
  Settings,
  LogOut,
  User,
  Building,
  Zap,
  Activity,
} from 'lucide-react';

export function Header() {
  const { user, currentOrganization, logout } = useAuthStore();
  const { systemHealth, activeConnections } = useAppStore();

  return (
    <header className="h-16 border-b border-gray-200 bg-white/80 backdrop-blur-sm flex items-center justify-between px-6">
      {/* Sidebar Trigger and Search */}
      <div className="flex items-center gap-4 flex-1 max-w-md">
        <SidebarTrigger />
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Search agents, tools, workflows..."
            className="pl-10 bg-gray-50/50 border-gray-200"
          />
        </div>
      </div>

      {/* Status Indicators */}
      <div className="flex items-center gap-4">
        {/* System Health */}
        <div className="flex items-center gap-2">
          <Activity className="w-4 h-4 text-gray-500" />
          <Badge variant={systemHealth === 'healthy' ? 'default' : 'destructive'} className="text-xs">
            {systemHealth}
          </Badge>
          <span className="text-sm text-gray-500">{activeConnections} active</span>
        </div>

        {/* Notifications */}
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="w-4 h-4" />
          <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
        </Button>

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center gap-3 px-3 py-2">
              <Avatar className="w-8 h-8">
                <AvatarImage src={user?.avatar} />
                <AvatarFallback>
                  {user?.name?.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="text-left">
                <div className="font-medium text-sm">{user?.name}</div>
                <div className="text-xs text-gray-500">{currentOrganization?.name}</div>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium">{user?.name}</p>
                <p className="text-xs text-gray-500">{user?.email}</p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              <span>Profile</span>
            </DropdownMenuItem>
            
            <DropdownMenuItem>
              <Building className="mr-2 h-4 w-4" />
              <span>Organization</span>
            </DropdownMenuItem>
            
            <DropdownMenuItem>
              <Zap className="mr-2 h-4 w-4" />
              <span>API Keys</span>
            </DropdownMenuItem>
            
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={logout}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>Log out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}