import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';
import {
  Brain,
  Wrench,
  GitBranch,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
} from 'lucide-react';

interface ActivityItem {
  id: string;
  type: 'agent' | 'tool' | 'workflow' | 'system';
  action: string;
  description: string;
  status: 'success' | 'warning' | 'error' | 'info';
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

const mockActivity: ActivityItem[] = [
  {
    id: '1',
    type: 'agent',
    action: 'Agent Execution',
    description: 'Customer Support Agent processed 12 inquiries',
    status: 'success',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    metadata: { agentName: 'Customer Support Agent', count: 12 }
  },
  {
    id: '2',
    type: 'workflow',
    action: 'Workflow Completed',
    description: 'Lead Generation workflow processed 8 new leads',
    status: 'success',
    timestamp: new Date(Date.now() - 12 * 60 * 1000),
    metadata: { workflowName: 'Lead Generation', leads: 8 }
  },
  {
    id: '3',
    type: 'tool',
    action: 'Tool Integration',
    description: 'Slack tool successfully connected',
    status: 'success',
    timestamp: new Date(Date.now() - 25 * 60 * 1000),
    metadata: { toolName: 'Slack' }
  },
  {
    id: '4',
    type: 'system',
    action: 'Provider Warning',
    description: 'OpenAI API rate limit approaching',
    status: 'warning',
    timestamp: new Date(Date.now() - 35 * 60 * 1000),
    metadata: { provider: 'OpenAI', usage: '85%' }
  },
  {
    id: '5',
    type: 'agent',
    action: 'Agent Created',
    description: 'New Data Analysis Agent deployed',
    status: 'info',
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    metadata: { agentName: 'Data Analysis Agent' }
  },
  {
    id: '6',
    type: 'workflow',
    action: 'Workflow Error',
    description: 'Email Automation workflow failed at step 3',
    status: 'error',
    timestamp: new Date(Date.now() - 52 * 60 * 1000),
    metadata: { workflowName: 'Email Automation', step: 3 }
  },
];

const getIcon = (type: ActivityItem['type']) => {
  switch (type) {
    case 'agent':
      return Brain;
    case 'tool':
      return Wrench;
    case 'workflow':
      return GitBranch;
    case 'system':
      return Zap;
    default:
      return MessageSquare;
  }
};

const getStatusIcon = (status: ActivityItem['status']) => {
  switch (status) {
    case 'success':
      return CheckCircle;
    case 'warning':
      return AlertTriangle;
    case 'error':
      return AlertTriangle;
    case 'info':
      return Clock;
    default:
      return Clock;
  }
};

const getStatusColor = (status: ActivityItem['status']) => {
  switch (status) {
    case 'success':
      return 'text-green-600 bg-green-50';
    case 'warning':
      return 'text-yellow-600 bg-yellow-50';
    case 'error':
      return 'text-red-600 bg-red-50';
    case 'info':
      return 'text-blue-600 bg-blue-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
};

export function RecentActivity() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Recent Activity
        </CardTitle>
        <CardDescription>
          Latest system events and operations
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-4">
            {mockActivity.map((activity) => {
              const TypeIcon = getIcon(activity.type);
              const StatusIcon = getStatusIcon(activity.status);
              
              return (
                <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex-shrink-0 mt-0.5">
                    <div className={`p-2 rounded-lg ${getStatusColor(activity.status)}`}>
                      <TypeIcon className="h-3 w-3" />
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm text-gray-900">
                        {activity.action}
                      </span>
                      <StatusIcon className={`h-3 w-3 ${
                        activity.status === 'success' ? 'text-green-600' :
                        activity.status === 'warning' ? 'text-yellow-600' :
                        activity.status === 'error' ? 'text-red-600' :
                        'text-blue-600'
                      }`} />
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      {activity.description}
                    </p>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {activity.type}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {formatDistanceToNow(activity.timestamp, { addSuffix: true })}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}