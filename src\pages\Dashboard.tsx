import { useEffect } from 'react';
import { OverviewCards } from '@/components/dashboard/overview-cards';
import { RecentActivity } from '@/components/dashboard/recent-activity';
import { SystemMetrics } from '@/components/dashboard/system-metrics';
import { useAppStore } from '@/lib/stores/app-store';
import { useAuthStore } from '@/lib/stores/auth-store';
import { MainLayout } from '@/components/layout/main-layout';
export default function Dashboard() {
  const { user } = useAuthStore();
  const { setActiveConnections, setSystemHealth } = useAppStore();

  useEffect(() => {
    // Simulate real-tim  e data updates
    const interval = setInterval(() => {
      setActiveConnections(Math.floor(Math.random() * 50) + 20);
      const healthStates = ['healthy', 'degraded', 'unhealthy'] as const;
      setSystemHealth(healthStates[Math.floor(Math.random() * 3)]);
    }, 5000);

    return () => clearInterval(interval);
  }, [setActiveConnections, setSystemHealth]);

  return (
    <MainLayout>
      <div className="space-y-8">
        {/* Welcome Header */}
        <div className="border-b border-gray-200 pb-6">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.name}
          </h1>
          <p className="mt-2 text-gray-600">
            Monitor your AI orchestration platform and track system performance in real-time.
        </p>
      </div>

      {/* Overview Cards */}
      <OverviewCards />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* System Metrics - Takes up 2 columns */}
        <div className="xl:col-span-2">
          <SystemMetrics />
        </div>

        {/* Recent Activity - Takes up 1 column */}
        <div className="xl:col-span-1">
          <RecentActivity />
        </div>
      </div>
    </div>
    </MainLayout>
  );
}