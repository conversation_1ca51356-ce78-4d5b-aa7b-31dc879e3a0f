import { useEffect } from 'react';
import { OverviewCards } from '@/components/dashboard/overview-cards';
import { RecentActivity } from '@/components/dashboard/recent-activity';
import { SystemMetrics } from '@/components/dashboard/system-metrics';
import { useAppStore } from '@/lib/stores/app-store';
import { useAuthStore } from '@/lib/stores/auth-store';

export default function Dashboard() {
  const { user, isAuthenticated } = useAuthStore();
  const { setActiveConnections, setSystemHealth } = useAppStore();

  // Initialize mock data
  useEffect(() => {
    if (isAuthenticated && user) {
      // Initialize stores with mock data for demonstration
      initializeMockData();
    }
  }, [isAuthenticated, user]);

  useEffect(() => {
    // Simulate real-time data updates
    const interval = setInterval(() => {
      setActiveConnections(Math.floor(Math.random() * 50) + 20);
      const healthStates = ['healthy', 'degraded', 'unhealthy'] as const;
      setSystemHealth(healthStates[Math.floor(Math.random() * 3)]);
    }, 5000);

    return () => clearInterval(interval);
  }, [setActiveConnections, setSystemHealth]);

  const initializeMockData = () => {
    const { setAgents, setTools, setProviders, setDocuments, setPrompts } = useAppStore.getState();

    // Mock agents
    setAgents([
      {
        id: '1',
        name: 'Customer Support Agent',
        description: 'Handles customer inquiries and support tickets',
        type: 'multi_task',
        providerId: 'openai',
        modelId: 'gpt-4',
        systemPrompt: 'You are a helpful customer support agent...',
        memory: { shortTerm: {}, longTerm: {}, episodic: [], semantic: {} },
        tools: ['1', '2'],
        isActive: true,
        performance: {
          totalInteractions: 1247,
          avgResponseTime: 342,
          successRate: 98.4,
          userSatisfaction: 4.7,
          costPerInteraction: 0.12,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '2',
        name: 'Data Analysis Agent',
        description: 'Analyzes data and generates insights',
        type: 'single_task',
        providerId: 'claude',
        modelId: 'claude-3-opus',
        systemPrompt: 'You are an expert data analyst...',
        memory: { shortTerm: {}, longTerm: {}, episodic: [], semantic: {} },
        tools: ['3', '4'],
        isActive: true,
        performance: {
          totalInteractions: 856,
          avgResponseTime: 298,
          successRate: 96.2,
          userSatisfaction: 4.5,
          costPerInteraction: 0.18,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);

    // Mock tools
    setTools([
      {
        id: '1',
        name: 'Slack Integration',
        description: 'Send messages and manage Slack channels',
        category: 'communication',
        version: '1.2.0',
        schema: {
          input: { channel: 'string', message: 'string' },
          output: { success: 'boolean', messageId: 'string' },
          required: ['channel', 'message'],
        },
        authentication: { type: 'oauth2', config: {} },
        isInstalled: true,
        healthStatus: 'healthy',
        usage: {
          executionCount: 234,
          successRate: 98.7,
          avgExecutionTime: 145,
          errorCount: 3,
        },
      },
      {
        id: '2',
        name: 'Gmail Integration',
        description: 'Send and manage emails via Gmail',
        category: 'communication',
        version: '1.1.5',
        schema: {
          input: { to: 'string', subject: 'string', body: 'string' },
          output: { success: 'boolean', messageId: 'string' },
          required: ['to', 'subject', 'body'],
        },
        authentication: { type: 'oauth2', config: {} },
        isInstalled: true,
        healthStatus: 'healthy',
        usage: {
          executionCount: 156,
          successRate: 97.4,
          avgExecutionTime: 267,
          errorCount: 4,
        },
      },
    ]);

    // Mock providers
    setProviders([
      {
        id: 'openai',
        name: 'OpenAI',
        type: 'openai',
        models: [
          {
            id: 'gpt-4',
            name: 'GPT-4',
            description: 'Most capable GPT-4 model',
            inputTokenCost: 0.03,
            outputTokenCost: 0.06,
            contextLength: 8192,
            capabilities: ['text', 'function_calling', 'json_mode'],
          },
        ],
        apiKey: 'sk-****',
        isActive: true,
        healthStatus: 'healthy',
        usage: {
          totalTokens: 2456789,
          totalCost: 245.67,
          requestCount: 1247,
          avgLatency: 342,
          errorRate: 0.02,
        },
      },
    ]);

    // Mock documents
    setDocuments([
      {
        id: '1',
        name: 'Company Handbook',
        type: 'pdf',
        content: 'Company policies and procedures...',
        metadata: {
          size: 2456789,
          language: 'en',
          author: 'HR Team',
          tags: ['hr', 'policies'],
          summary: 'Comprehensive company handbook',
        },
        embeddings: [],
        isProcessed: true,
        uploadedAt: new Date(),
      },
    ]);

    // Mock prompts
    setPrompts([
      {
        id: '1',
        name: 'Customer Support Prompt',
        description: 'Standard prompt for customer support interactions',
        template: 'You are a helpful customer support agent. Please assist the user with their inquiry: {{user_message}}',
        variables: [
          { name: 'user_message', type: 'string', required: true },
        ],
        versions: [
          {
            id: '1',
            version: '1.0.0',
            template: 'You are a helpful customer support agent. Please assist the user with their inquiry: {{user_message}}',
            changelog: 'Initial version',
            createdAt: new Date(),
            isActive: true,
          },
        ],
        performance: {
          totalUsage: 1247,
          avgQualityScore: 4.6,
          avgResponseTime: 342,
          successRate: 98.4,
        },
      },
    ]);
  };

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="border-b border-gray-200 pb-6">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.name}
        </h1>
        <p className="mt-2 text-gray-600">
          Monitor your AI orchestration platform and track system performance in real-time.
        </p>
      </div>

      {/* Overview Cards */}
      <OverviewCards />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* System Metrics - Takes up 2 columns */}
        <div className="xl:col-span-2">
          <SystemMetrics />
        </div>

        {/* Recent Activity - Takes up 1 column */}
        <div className="xl:col-span-1">
          <RecentActivity />
        </div>
      </div>
    </div>
  );
}