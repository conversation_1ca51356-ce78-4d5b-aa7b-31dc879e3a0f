import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAppStore } from '@/lib/stores/app-store';
import {
  Brain,
  Wrench,
  GitBranch,
  Zap,
  TrendingUp,
  TrendingDown,
  Activity,
  DollarSign,
} from 'lucide-react';

export function OverviewCards() {
  const { agents, tools, workflows, providers } = useAppStore();

  const stats = [
    {
      title: 'Active Agents',
      value: agents.filter(a => a.isActive).length,
      total: agents.length,
      icon: Brain,
      trend: '+12%',
      trendUp: true,
      description: 'AI agents running',
    },
    {
      title: 'Installed Tools',
      value: tools.filter(t => t.isInstalled).length,
      total: tools.length,
      icon: Wrench,
      trend: '+3',
      trendUp: true,
      description: 'Business tools integrated',
    },
    {
      title: 'Workflows',
      value: workflows.filter(w => w.isActive).length,
      total: workflows.length,
      icon: GitBranch,
      trend: '+8%',
      trendUp: true,
      description: 'Automated workflows',
    },
    {
      title: 'AI Providers',
      value: providers.filter(p => p.isActive).length,
      total: providers.length,
      icon: Zap,
      trend: 'All healthy',
      trendUp: true,
      description: 'Connected providers',
    },
  ];

  const metrics = [
    {
      title: 'Total Interactions',
      value: '12,847',
      change: '+18.2%',
      changeUp: true,
      icon: Activity,
      description: 'vs last month',
    },
    {
      title: 'Success Rate',
      value: '98.4%',
      change: '+0.8%',
      changeUp: true,
      icon: TrendingUp,
      description: 'System reliability',
    },
    {
      title: 'Avg Response Time',
      value: '342ms',
      change: '-12ms',
      changeUp: true,
      icon: Activity,
      description: 'Response latency',
    },
    {
      title: 'Monthly Cost',
      value: '$1,247',
      change: '-5.2%',
      changeUp: true,
      icon: DollarSign,
      description: 'AI provider costs',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Core Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title} className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <div className="p-2 bg-blue-50 rounded-lg">
                  <Icon className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-baseline gap-2">
                  <div className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-500">
                    / {stat.total}
                  </div>
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <Badge 
                    variant={stat.trendUp ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {stat.trend}
                  </Badge>
                  <p className="text-xs text-gray-500">{stat.description}</p>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric) => {
          const Icon = metric.icon;
          return (
            <Card key={metric.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {metric.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-gray-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {metric.value}
                </div>
                <div className="flex items-center gap-1 mt-1">
                  {metric.changeUp ? (
                    <TrendingUp className="h-3 w-3 text-green-600" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-600" />
                  )}
                  <span className={`text-xs font-medium ${
                    metric.changeUp ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {metric.change}
                  </span>
                  <span className="text-xs text-gray-500">
                    {metric.description}
                  </span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}