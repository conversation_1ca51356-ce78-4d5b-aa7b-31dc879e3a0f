import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from 'recharts';
import {
  Cpu,
  Database,
  Network,
  Clock,
  DollarSign,
  Zap,
} from 'lucide-react';

// Mock data for charts
const performanceData = [
  { time: '00:00', cpu: 45, memory: 62, network: 28 },
  { time: '04:00', cpu: 52, memory: 58, network: 35 },
  { time: '08:00', cpu: 78, memory: 71, network: 52 },
  { time: '12:00', cpu: 65, memory: 69, network: 48 },
  { time: '16:00', cpu: 71, memory: 73, network: 51 },
  { time: '20:00', cpu: 58, memory: 66, network: 42 },
];

const usageData = [
  { provider: 'OpenAI', requests: 1247, cost: 245, latency: 342 },
  { provider: '<PERSON>', requests: 856, cost: 198, latency: 298 },
  { provider: 'Gemini', requests: 634, cost: 112, latency: 456 },
  { provider: 'Groq', requests: 445, cost: 89, latency: 187 },
];

const responseTimeData = [
  { time: '00:00', avg: 325, p95: 487, p99: 612 },
  { time: '04:00', avg: 298, p95: 445, p99: 578 },
  { time: '08:00', avg: 367, p95: 523, p99: 689 },
  { time: '12:00', avg: 342, p95: 498, p99: 634 },
  { time: '16:00', avg: 356, p95: 512, p99: 647 },
  { time: '20:00', avg: 331, p95: 476, p99: 598 },
];

export function SystemMetrics() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* System Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cpu className="h-5 w-5" />
            System Performance
          </CardTitle>
          <CardDescription>
            Real-time system resource utilization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 mb-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Cpu className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">CPU Usage</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">71%</span>
                <Badge variant="default" className="text-xs">Normal</Badge>
              </div>
            </div>
            <Progress value={71} className="h-2" />
            
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Database className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Memory Usage</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">73%</span>
                <Badge variant="default" className="text-xs">Normal</Badge>
              </div>
            </div>
            <Progress value={73} className="h-2" />
            
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Network className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium">Network I/O</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">51%</span>
                <Badge variant="default" className="text-xs">Normal</Badge>
              </div>
            </div>
            <Progress value={51} className="h-2" />
          </div>
          
          <ResponsiveContainer width="100%" height={200}>
            <AreaChart data={performanceData}>
              <defs>
                <linearGradient id="cpu" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="memory" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#10B981" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="time" fontSize={12} />
              <YAxis fontSize={12} />
              <Tooltip />
              <Area
                type="monotone"
                dataKey="cpu"
                stroke="#3B82F6"
                fillOpacity={1}
                fill="url(#cpu)"
                name="CPU %"
              />
              <Area
                type="monotone"
                dataKey="memory"
                stroke="#10B981"
                fillOpacity={1}
                fill="url(#memory)"
                name="Memory %"
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Response Times */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Response Times
          </CardTitle>
          <CardDescription>
            API response time metrics over 24 hours
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">342ms</div>
              <div className="text-xs text-gray-500">Average</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">498ms</div>
              <div className="text-xs text-gray-500">95th percentile</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">634ms</div>
              <div className="text-xs text-gray-500">99th percentile</div>
            </div>
          </div>
          
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={responseTimeData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="time" fontSize={12} />
              <YAxis fontSize={12} />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="avg"
                stroke="#3B82F6"
                strokeWidth={2}
                name="Average"
                dot={false}
              />
              <Line
                type="monotone"
                dataKey="p95"
                stroke="#F59E0B"
                strokeWidth={2}
                name="95th percentile"
                dot={false}
              />
              <Line
                type="monotone"
                dataKey="p99"
                stroke="#EF4444"
                strokeWidth={2}
                name="99th percentile"
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Provider Usage */}
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            AI Provider Usage
          </CardTitle>
          <CardDescription>
            Usage statistics and costs across AI providers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">3,182</div>
              <div className="text-xs text-gray-500">Total Requests</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">$644</div>
              <div className="text-xs text-gray-500">Total Cost</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">98.7%</div>
              <div className="text-xs text-gray-500">Success Rate</div>
            </div>
          </div>
          
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={usageData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="provider" fontSize={12} />
              <YAxis yAxisId="left" orientation="left" fontSize={12} />
              <YAxis yAxisId="right" orientation="right" fontSize={12} />
              <Tooltip />
              <Bar yAxisId="left" dataKey="requests" fill="#3B82F6" name="Requests" />
              <Bar yAxisId="right" dataKey="cost" fill="#10B981" name="Cost ($)" />
            </BarChart>
          </ResponsiveContainer>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
            {usageData.map((provider) => (
              <div key={provider.provider} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-sm">{provider.provider}</h4>
                  <Badge variant="outline" className="text-xs">
                    {provider.latency}ms
                  </Badge>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span className="text-gray-500">Requests</span>
                    <span className="font-medium">{provider.requests.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-gray-500">Cost</span>
                    <span className="font-medium">${provider.cost}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}