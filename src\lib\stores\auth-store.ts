import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, UserOrganization } from '@/types';

interface AuthState {
  user: User | null;
  token: string | null;
  currentOrganization: UserOrganization | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (email: string, password: string, name: string) => Promise<void>;
  setCurrentOrganization: (org: UserOrganization) => void;
  refreshUser: () => Promise<void>;
  clearError: () => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      currentOrganization: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });
        
        try {
          // Simulate API call - replace with actual implementation
          const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password }),
          });

          if (!response.ok) {
            throw new Error('Invalid credentials');
          }

          const { user, token } = await response.json();
          
          set({
            user,
            token,
            currentOrganization: user.organizations[0] || null,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Login failed',
            isLoading: false,
          });
        }
      },

      register: async (email: string, password: string, name: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password, name }),
          });

          if (!response.ok) {
            throw new Error('Registration failed');
          }

          const { user, token } = await response.json();
          
          set({
            user,
            token,
            currentOrganization: user.organizations[0] || null,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Registration failed',
            isLoading: false,
          });
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          currentOrganization: null,
          isAuthenticated: false,
          error: null,
        });
      },

      setCurrentOrganization: (org: UserOrganization) => {
        set({ currentOrganization: org });
      },

      refreshUser: async () => {
        const { token } = get();
        if (!token) return;

        try {
          const response = await fetch('/api/auth/me', {
            headers: { Authorization: `Bearer ${token}` },
          });

          if (response.ok) {
            const user = await response.json();
            set({ user });
          }
        } catch (error) {
          console.error('Failed to refresh user:', error);
        }
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'synapseai-auth',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        currentOrganization: state.currentOrganization,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);